/* Retro Theme - Inspired by old-school monochrome computer terminals */

:root {
  /* Base colors */
  --retro-bg: #000000;
  --retro-text: #33ff33; /* Bright green */
  --retro-dim: #1a8c1a; /* Dimmer green */
  --retro-highlight: #5fff5f; /* Brighter green for highlights */
  --retro-enemy: #ffaa00; /* Amber/orange for enemy units */
  --retro-enemy-dim: #aa7700; /* Dimmer amber for enemy */
  --retro-border: #33ff33;
  --retro-panel-bg: rgba(0, 20, 0, 0.85);
  --retro-input-bg: rgba(0, 40, 0, 0.5);
  --retro-button-bg: #1a8c1a;
  --retro-button-hover: #33ff33;
  --retro-shadow: 0 0 10px rgba(51, 255, 51, 0.5);

  /* Spacing remains the same */
  --spacing-unit: 8px;
}

/* Apply retro theme to body */
body.retro-mode {
  background-color: var(--retro-bg);
  color: var(--retro-text);
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 5px rgba(51, 255, 51, 0.7);
}

/* Header styles */
body.retro-mode h1,
body.retro-mode h2,
body.retro-mode h3,
body.retro-mode h4 {
  color: var(--retro-highlight);
  text-shadow: 0 0 8px rgba(95, 255, 95, 0.7);
}

/* Panel backgrounds */
body.retro-mode .control-panel,
body.retro-mode #controls-container,
body.retro-mode #mortar-controls-container,
body.retro-mode .collapsible,
body.retro-mode .content {
  background-color: var(--retro-panel-bg);
  border: 1px solid var(--retro-dim);
  box-shadow: var(--retro-shadow);
}

/* Form elements */
body.retro-mode input,
body.retro-mode select,
body.retro-mode textarea {
  background-color: var(--retro-input-bg);
  color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
  font-family: 'Courier New', monospace;
}

body.retro-mode input:focus,
body.retro-mode select:focus,
body.retro-mode textarea:focus {
  border-color: var(--retro-highlight);
  outline: none;
  box-shadow: 0 0 5px var(--retro-highlight);
}

/* Buttons */
body.retro-mode button {
  background-color: var(--retro-button-bg);
  color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
}

body.retro-mode button:hover:not(:disabled) {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
  box-shadow: 0 0 10px var(--retro-highlight);
}

body.retro-mode button:disabled {
  background-color: rgba(26, 140, 26, 0.3);
  color: rgba(51, 255, 51, 0.5);
  cursor: not-allowed;
}

/* Target list */
body.retro-mode #targets-list li,
body.retro-mode #received-targets-list li {
  border-color: var(--retro-dim);
  background-color: rgba(0, 30, 0, 0.5);
}

body.retro-mode #targets-list li:hover,
body.retro-mode #received-targets-list li:hover {
  background-color: rgba(0, 50, 0, 0.7);
}

body.retro-mode #targets-list li.selected,
body.retro-mode #received-targets-list li.selected {
  background-color: rgba(0, 70, 0, 0.9);
  border-color: var(--retro-highlight);
}

/* Map container */
body.retro-mode #map-container,
body.retro-mode #mortar-map-container {
  border: 1px solid var(--retro-dim);
  box-shadow: var(--retro-shadow);
}

/* Map legend */
body.retro-mode #map-legend {
  background-color: var(--retro-panel-bg);
  border: 1px solid var(--retro-dim);
}

body.retro-mode #map-legend.collapsed {
  background-color: rgba(0, 20, 0, 0.7);
}

body.retro-mode #map-legend.collapsed:hover {
  background-color: rgba(0, 30, 0, 0.9);
}

/* Force type indicators for the legend */
body.retro-mode .legend-friendly {
  background-color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
}

body.retro-mode .legend-enemy {
  background-color: var(--retro-enemy);
  border: 1px solid var(--retro-enemy-dim);
}

/* Target markers on map */
body.retro-mode .target-marker.target-friendly div {
  background-color: var(--retro-highlight) !important;
  border-color: var(--retro-dim) !important;
}

body.retro-mode .target-marker.target-enemy div {
  background-color: var(--retro-enemy) !important;
  border-color: var(--retro-enemy-dim) !important;
}

/* Mortar position marker */
body.retro-mode .mortar-position-marker div {
  background-color: var(--retro-highlight) !important;
  border-color: var(--retro-dim) !important;
}

/* Glow animations */
@keyframes retro-friendly-glow {
  0% { box-shadow: 0 0 0 0 rgba(51, 255, 51, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(51, 255, 51, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(51, 255, 51, 0.7); }
}

@keyframes retro-enemy-glow {
  0% { box-shadow: 0 0 0 0 rgba(255, 170, 0, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(255, 170, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(255, 170, 0, 0.7); }
}

body.retro-mode .target-marker.target-friendly div {
  animation: retro-friendly-glow 2s infinite;
}

body.retro-mode .target-marker.target-enemy div {
  animation: retro-enemy-glow 2s infinite;
}

/* CRT effect - subtle scanlines */
body.retro-mode::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    rgba(18, 16, 16, 0) 50%,
    rgba(0, 0, 0, 0.25) 50%
  );
  background-size: 100% 4px;
  z-index: 9999;
  pointer-events: none;
  opacity: 0.15;
}

/* Retro cursor */
body.retro-mode * {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style="fill:none;stroke:%2333ff33;stroke-width:2px;"><rect width="8" height="16"/></svg>') 4 8, auto !important;
}

/* Theme settings section */
#theme-settings {
  margin-top: 20px;
}

/* Theme toggle button */
.theme-toggle-button {
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  background-color: #444;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
  text-align: center;
}

body.retro-mode .theme-toggle-button {
  background-color: var(--retro-button-bg);
  color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
}

.theme-toggle-button:hover {
  background-color: #555;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

body.retro-mode .theme-toggle-button:hover {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
  box-shadow: 0 0 10px var(--retro-highlight);
}

/* Leaflet map overrides for retro mode */
body.retro-mode .leaflet-container {
  background: #001400;
}

body.retro-mode .leaflet-tile {
  filter: grayscale(100%) brightness(30%) sepia(100%) hue-rotate(70deg) saturate(400%) brightness(50%);
}

body.retro-mode .leaflet-control-zoom a {
  background-color: var(--retro-panel-bg);
  color: var(--retro-text);
  border-color: var(--retro-dim);
}

body.retro-mode .leaflet-control-zoom a:hover {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
}

/* Retro-style scrollbar */
body.retro-mode::-webkit-scrollbar {
  width: 12px;
}

body.retro-mode::-webkit-scrollbar-track {
  background: var(--retro-bg);
}

body.retro-mode::-webkit-scrollbar-thumb {
  background-color: var(--retro-dim);
  border-radius: 0;
  border: 1px solid var(--retro-highlight);
}

body.retro-mode::-webkit-scrollbar-thumb:hover {
  background-color: var(--retro-highlight);
}

/* Blinking cursor effect for inputs */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

body.retro-mode input:focus::after {
  content: '|';
  margin-left: 2px;
  animation: blink 1s step-end infinite;
}
